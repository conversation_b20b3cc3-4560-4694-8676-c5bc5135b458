<template>
  <view class="register-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题区域 -->
      <view class="title-section">
        <text class="main-title">Sign up</text>
        <text class="subtitle">When entering data, please pay attention to its accuracy.</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 姓名输入 -->
        <FormInput
          label="Name"
          v-model="formData.name"
          type="text"
          placeholder="Enter your name"
          prefix-icon="👤"
          :disabled="loading"
          :error="errors.name"
          @input="handleNameInput"
        />

        <!-- 姓氏输入 -->
        <FormInput
          label="Last name"
          v-model="formData.lastName"
          type="text"
          placeholder="Enter your last name"
          prefix-icon="👤"
          :disabled="loading"
          :error="errors.lastName"
          @input="handleLastNameInput"
        />

        <!-- 邮箱输入 -->
        <FormInput
          label="Email"
          v-model="formData.email"
          type="text"
          placeholder="Enter your email"
          prefix-icon="📧"
          :disabled="loading"
          :error="errors.email"
          @input="handleEmailInput"
        />

        <!-- 手机号输入 -->
        <FormInput
          label="Mobile number"
          v-model="formData.mobile"
          type="text"
          placeholder="+994 xx - xxx - xx - xx"
          prefix-icon="📱"
          :disabled="loading"
          :error="errors.mobile"
          @input="handleMobileInput"
        />

        <!-- 服务条款同意 -->
        <view class="terms-section">
          <view class="checkbox-wrapper" @tap="toggleTermsAgreement">
            <view class="checkbox" :class="{ 'checked': formData.agreeToTerms }">
              <text v-if="formData.agreeToTerms" class="check-icon">✓</text>
            </view>
            <view class="terms-text">
              <text class="terms-normal">I have read and agreed. </text>
              <text class="terms-link" @tap.stop="viewTerms">Terms of Service</text>
            </view>
          </view>
          <view v-if="errors.agreeToTerms" class="error-text">
            {{ errors.agreeToTerms }}
          </view>
        </view>

        <!-- 错误提示 -->
        <view v-if="errors.general" class="error-message">
          {{ errors.general }}
        </view>

        <!-- 注册按钮 -->
        <FormButton
          text="Next"
          type="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!canSubmit"
          loading-text="Creating..."
          @click="handleRegister"
        />

        <!-- 登录链接 -->
        <view class="login-section">
          <text class="login-text">Do you currently have an account? </text>
          <FormLink
            text="Sign in"
            type="primary"
            size="large"
            @click="goToLogin"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { createValidator, validators } from '@/utils/form-validator.js';
import { AuthHelper } from '@/utils/auth-helper.js';
import FormInput from '@/components/form/FormInput.vue';
import FormButton from '@/components/form/FormButton.vue';
import FormLink from '@/components/form/FormLink.vue';

export default {
  components: {
    FormInput,
    FormButton,
    FormLink
  },

  data() {
    return {
      formData: {
        name: '',
        lastName: '',
        email: '',
        mobile: '',
        agreeToTerms: false
      },
      loading: false,
      errors: {},
      validator: null
    };
  },

  created() {
    // 创建表单验证器
    this.validator = createValidator('register');
  },

  computed: {
    canSubmit() {
      return this.formData.name.trim() &&
             this.formData.lastName.trim() &&
             this.formData.email.trim() &&
             this.formData.mobile.trim() &&
             this.formData.agreeToTerms &&
             !this.loading;
    }
  },

  onLoad() {
    // 页面加载时的初始化
  },

  methods: {
    // 处理姓名输入
    handleNameInput(value) {
      this.formData.name = value;
      this.clearError('name');
      this.clearError('general');
    },

    // 处理姓氏输入
    handleLastNameInput(value) {
      this.formData.lastName = value;
      this.clearError('lastName');
      this.clearError('general');
    },

    // 处理邮箱输入
    handleEmailInput(value) {
      this.formData.email = value;
      this.clearError('email');
      this.clearError('general');
    },

    // 处理手机号输入
    handleMobileInput(value) {
      this.formData.mobile = value;
      this.clearError('mobile');
      this.clearError('general');
    },

    // 切换服务条款同意状态
    toggleTermsAgreement() {
      this.formData.agreeToTerms = !this.formData.agreeToTerms;
      this.clearError('agreeToTerms');
    },

    // 查看服务条款
    viewTerms() {
      uni.showToast({
        title: '服务条款功能开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // 表单验证
    validateForm() {
      const { isValid, errors } = this.validator.validate(this.formData);
      this.errors = errors;
      return isValid;
    },

    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field);
      }
    },

    // 处理注册
    async handleRegister() {
      // 验证服务条款同意
      if (!this.formData.agreeToTerms) {
        this.errors.agreeToTerms = '请同意服务条款';
        return;
      }

      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        const result = await AuthHelper.register({
          name: this.formData.name.trim(),
          lastName: this.formData.lastName.trim(),
          email: this.formData.email.trim(),
          mobile: this.formData.mobile.trim()
        });

        if (result.success) {
          // 注册成功，显示提示并跳转
          uni.showToast({
            title: '注册成功',
            icon: 'success',
            duration: 2000
          });

          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 2000);
        } else {
          this.errors.general = result.error || '注册失败，请重试';
        }

      } catch (error) {
        console.error('注册失败:', error);
        this.errors.general = error.message || '注册失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    // 跳转到登录页面
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      });
    }
  }
};
</script>

<style scoped>
.register-container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 80rpx;
}
/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60rpx;
}

/* 标题区域 */
.title-section {
  width: 100%;
  text-align: center;
  margin-top: 92rpx;
  margin-bottom: 42rpx;
}

.main-title {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 60rpx;
  font-weight: bold;
  color: #F2282D;
  line-height: 80rpx;
  display: block;
  margin-bottom: 16rpx;
}

.subtitle {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30rpx;
  font-weight: 500;
  color: #8A8A8A;
  line-height: 44rpx;
  display: block;
}

/* 表单区域 */
.form-section {
  width: 100%;
  max-width: 670rpx;
}

/* 服务条款区域 */
.terms-section {
  margin: 32rpx 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  cursor: pointer;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 8rpx;
  background: #D2D2D2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 6rpx;
}

.checkbox.checked {
  background: #F2282D;
}

.check-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.terms-text {
  flex: 1;
  line-height: 48rpx;
}

.terms-normal {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24rpx;
  font-weight: 500;
  color: #000000;
}

.terms-link {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24rpx;
  font-weight: normal;
  color: #1676F3;
  text-decoration: underline;
  cursor: pointer;
}

/* 错误提示 */
.error-text {
  color: #F2282D;
  font-size: 14rpx;
  margin-top: 8rpx;
  text-align: center;
}

.error-message {
  background: #ffe6e6;
  color: #F2282D;
  padding: 12rpx;
  border-radius: 12rpx;
  font-size: 14rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

/* 注册按钮区域 */
.form-section :deep(.form-button--large) {
  margin-top: 32rpx;
  margin-bottom: 28rpx;
}
}

/* 登录链接 */
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  margin-top: 28rpx;
  margin-left: 20rpx;
}

.login-text {
  font-family: 'Poppins', sans-serif;
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  letter-spacing: -0.3rpx;
  line-height: normal;
}
</style>
